<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Events\DomainHistoryEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\Events\EmailSentEvent;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }
    public function createDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();
        return $this->processCreateRequest($data);
    }

    public function processApproveRequestFromForm($request)
    {
        $domainInfo = $this->getDomainInfoForRequest($request->domainId);
        $adminContext = $this->getAdminContext();
        $supportNote = $request->support_note ?? "Request approved by {$adminContext['email']}";

        $data = [
            'domainId' => $domainInfo->domainId,
            'domainName' => $domainInfo->domainName,
            'userID' => $domainInfo->userID,
            'userEmail' => $domainInfo->userEmail,
            'reason' => $domainInfo->reason,
            'support_note' => $supportNote,
            'createdDate' => $domainInfo->requested_at ?? now()->toDateTimeString(),
            'adminId' => $adminContext['id'],
            'adminName' => $adminContext['name'],
            'adminEmail' => $adminContext['email']
        ];

        $this->processApproveRequest($data);

        return ['status' => 'success', 'message' => 'Domain deletion request approved successfully'];
    }

    public function processRejectRequestFromForm($request)
    {
        $domainInfo = $this->getDomainInfoForRequest($request->domainId);
        $supportNote = $request->support_note;

        $data = [
            'domainId' => $domainInfo->domainId,
            'domainName' => $domainInfo->domainName,
            'userID' => $domainInfo->userID,
            'userId' => $domainInfo->userID,
            'userEmail' => $domainInfo->userEmail,
            'reason' => $domainInfo->reason,
            'support_note' => $supportNote,
            'supportNote' => $supportNote,
            'createdDate' => $domainInfo->requested_at ?? now()->toDateTimeString()
        ];

        $this->processRejectRequest($data);

        return ['status' => 'success', 'message' => 'Domain deletion request rejected successfully'];
    }

    public function processCancelRequestFromForm($request)
    {
        $domainInfo = $this->getDomainInfoForRequest($request->domainId);
        $supportNote = $request->support_note;

        $data = [
            'domainId' => $domainInfo->domainId,
            'domainName' => $domainInfo->domainName,
            'userID' => $domainInfo->userID,
            'userId' => $domainInfo->userID,
            'userEmail' => $domainInfo->userEmail,
            'reason' => $domainInfo->reason,
            'support_note' => $supportNote,
            'supportNote' => $supportNote,
            'createdDate' => $domainInfo->requested_at ?? now()->toDateTimeString()
        ];

        $this->processCancelRequest($data);

        return ['status' => 'success', 'message' => 'Domain deletion request cancelled successfully'];
    }

    public function approveDeleteRequest($request)
    {
        return $this->processApproveRequestFromForm($request);
    }

    public function rejectDeleteRequest($request)
    {
        return $this->processRejectRequestFromForm($request);
    }

    public function cancelDeleteRequest($request)
    {
        return $this->processCancelRequestFromForm($request);
    }

    private function getDomainInfoForRequest($domainId)
    {
        $domainInfo = DatabaseQueryService::instance()->getDomainInfo($domainId);

        if (!$domainInfo) {
            app(AuthLogger::class)->error("Domain info not found for domainId: {$domainId}. This might indicate the domain cancellation request doesn't exist or there's a data integrity issue.");
            throw new \Exception("Domain cancellation request not found for domain ID: {$domainId}");
        }

        return $domainInfo;
    }

    public function processExpiredRequests()
    {
        $approvedRequests = DatabaseQueryService::instance()->getExpiredApprovedRequests();

        foreach ($approvedRequests as $request) {
            $data = $this->prepareRequestData($request);
            $this->processApprovedRequest($data);
        }

        return count($approvedRequests);
    }

    // PRIVATE FUNCTION

    private function prepareRequestData($request): array
    {
        return [
            'domainId' => $request->domainId,
            'domainName' => $request->domainName,
            'userId' => $request->userID,
            'userEmail' => $request->userEmail,
            'reason' => $request->reason,
            'createdDate' => $request->approvedDate,
            'adminId' => $request->support_agent_id,
            'adminName' => 'System',
            'adminEmail' => '<EMAIL>',
            'supportNote' => "Deletion processed automatically after 24+ hours"
        ];
    }

    private function processCreateRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete created by {$adminContext['email']}";

        $this->updateDomainStatusDeletion($data['domainId']);
        $this->createDomainCancellationRequest($data, $adminContext, $supportNote);
        $this->sendUserNotification($data, 'Domain Deletion Request Created', 'A deletion request for your domain "' . $data['domainName'] . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
        $this->sendUserEmail($data, 'Domain Deletion Request Created', 'A deletion request for your domain "' . $data['domainName'] . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
    }

    private function processApproveRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete approved by {$adminContext['email']}";

        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success', 'Domain deletion request approved by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');

        $this->markAsApproved($data, $adminContext, $supportNote);
        $this->sendUserNotification($data, 'Domain Deletion Request Approved','Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete.');
        $this->sendUserEmail($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
    }

    private function processRejectRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['support_note'] ?? "Request delete rejected by {$adminContext['email']}";

        $this->rejectDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->reactivateDomain($data['domainId']);
        $this->sendRejectionNotification($data, $adminContext);
        $this->logDomainRejectionHistory($data, $adminContext);
    }

    private function processCancelRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['support_note'];

        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success','Domain deletion request cancelled by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');

        $this->cancelDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->reactivateDomain($data['domainId']);
        $this->sendCancellationNotification($data, $adminContext);
    }

    private function processApprovedRequest(array $data): void
    {
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail']
        );
    }

    private function getAdminContext(): array
    {
        return [
            'id' => Auth::id() ?? 1,
            'name' => Auth::user()->name ?? 'System',
            'email' => Auth::user()->email ?? '<EMAIL>'
        ];
    }

    private function updateDomainStatusDeletion($domainId): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::IN_PROCESS,
                'updated_at' => now(),
            ]);
    }

    private function reactivateDomain($domainId): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => DomainStatus::ACTIVE,
                'updated_at' => now(),
            ]);
    }

    private function createDomainCancellationRequest(array $data, array $adminContext, string $supportNote): void
    {
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'] ?? 'Domain deletion request by ' . $adminFullName,
            'requested_at'        => now(),
            'support_agent_id'    => $adminContext['id'],
            'support_agent_name'  => $adminFullName,
            'feedback_date'       => now(),
            'support_note'        => $supportNote,
            'deleted_at'          => now(),
            'is_refunded'         => false,
        ]);
    }

    private function markAsApproved(array $data, array $adminContext, string $supportNote): void
    {
        $date = Carbon::parse($data['createdDate'] ?? now());
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? true : false;
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        app(AuthLogger::class)->info("Domain creation date: " . $date->toDateTimeString() . ", Current date: " . now()->toDateTimeString() . ", Is refunded: " . ($is_refunded ? 'true' : 'false'));

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id'   => $adminContext['id'],
                'support_agent_name' => $adminFullName,
                'feedback_date'      => now(),
                'support_note'       => $supportNote,
                'is_refunded'        => $is_refunded,
                'deleted_at'         => now(),
            ]);
         app(AuthLogger::class)->info("The domain is refunded: " . ($is_refunded ? 'true' : 'false'));
    }

    private function rejectDomainDeletionRequest($domainInfo, array $adminContext, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domainInfo->domainId)
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => now(),
            ]);
    }

    private function cancelDomainDeletionRequest($domainInfo, array $adminContext, string $supportNote): void
    {
        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domainInfo->domainId)
            ->update([
                'support_agent_id' => $adminContext['id'],
                'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
                'support_note' => $supportNote,
                'feedback_date' => null,
                'deleted_at' => now(),
            ]);

        $this->reactivateDomain($domainInfo->domainId);
    }

    private function sendUserNotification(array $data, string $title, string $message): void
    {

        DB::client()->table('notifications')->insert([
            'user_id'      => $data['userID'],
            'title'        => $title,
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'updated_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function sendUserEmail(array $data, string $subject, string $body): void
    {
        if (!isset($data['userEmail']) || !isset($data['domainName'])) return;

        $message = [
            'subject'  => $subject,
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($data['userEmail'])->send(new UserDeleteRequestMail($message));
        event(new EmailSentEvent ($data['userID'], $data['userName'], $data['userEmail'], $subject, 'Domain Deletion Request', json_encode($message), null));
    }

    private function sendRejectionNotification(array $data, array $adminContext): void
    {
        $message = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by ' . $adminContext['name'] . '.';

        $this->sendUserNotification($data, 'Domain Deletion Request Rejected', $message);

        $emailBody = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by our support team. If you have any questions, please contact our support.';
        $this->sendUserEmail($data, 'Domain Deletion Request Rejected', $emailBody);
    }

    private function sendCancellationNotification(array $data, array $adminContext): void
    {
        $message = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by ' . $adminContext['name'] . '.';

        $this->sendUserNotification($data, 'Domain Deletion Request Cancelled', $message);

        $emailBody = 'Your domain deletion request for "' . $data['domainName'] . '" has been cancelled by our support team. Your domain remains active.';
        $this->sendUserEmail($data, 'Domain Deletion Request Cancelled', $emailBody);
    }


    private function logDomainHistory(array $data, string $type, string $status, string $message): void
    {
        event(new DomainHistoryEvent(['domain_id' => $data['domainId'],'type' => $type,'status' => $status,'user_id' => $data['userID'] ?? null ,'message' => $message,'payload' => json_encode($data),]));
    }

    private function logDomainRejectionHistory(array $data, array $adminContext): void
    {
        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success','Domain deletion request rejected by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');
    }
}
