[2025-09-03 01:26:54] local.INFO: EPP Info Response{"data":{"name":"ahalawsa.net","registrant":"mj11755500358","registryDomainId":"140473645_DOMAIN_NET-VRSN","expiry":"2026-09-03T01:18:59Z","updated":"2025-09-03T01:18:59Z","created":"2025-09-03T01:18:59Z","domainPrivacyProtection":true,"contacts":{"tech":"mj11755500358","admin":"mj11755500358","billing":"mj11755500358"},"status":["inactive"],"hosts":[],"nameservers":[],"extensions":[{"rgpExtStatus":["addPeriod"]}]},"message":"showing info of ahalawsa.net","status":"OK","statusCode":200}  
[2025-09-03 01:26:56] local.INFO: Successfully removed clientDeleteProhibited status for domain: ahalawsa.net  
[2025-09-03 01:26:58] local.INFO: Successfully deleted domain from EPP and datastore: ahalawsa.net  
[2025-09-03 01:27:04] local.ERROR: {"query":[],"parameter":{"domainName":"ahalawsa.net","userEmail":"<EMAIL>","domainId":139,"createdDate":"2025-09-03 01:18:31","userID":7,"support_note":"tes testset set set ewst set","email":"<EMAIL>"},"error":"ErrorException","message":"Trying to access array offset on value of type null","code":0}  
[2025-09-03 01:42:24] local.INFO: EPP Info Response{"data":{"name":"ahalawsaw.com","registrant":"mj11755500358","registryDomainId":"140473644_DOMAIN_COM-VRSN","expiry":"2026-09-03T01:18:57Z","updated":"2025-09-03T01:18:57Z","created":"2025-09-03T01:18:57Z","domainPrivacyProtection":true,"contacts":{"tech":"mj11755500358","admin":"mj11755500358","billing":"mj11755500358"},"status":["inactive"],"hosts":[],"nameservers":[],"extensions":[{"rgpExtStatus":["addPeriod"]}]},"message":"showing info of ahalawsaw.com","status":"OK","statusCode":200}  
[2025-09-03 01:42:25] local.INFO: Successfully removed clientDeleteProhibited status for domain: ahalawsaw.com  
[2025-09-03 01:42:27] local.INFO: Successfully deleted domain from EPP and datastore: ahalawsaw.com  
[2025-09-03 01:42:31] local.ERROR: {"query":[],"parameter":{"domainName":"ahalawsaw.com","userEmail":"<EMAIL>","domainId":141,"createdDate":"2025-09-03 01:18:31","userID":7,"support_note":"tes tset set set setset set asd asd","email":"<EMAIL>"},"error":"Symfony\\Component\\Mailer\\Exception\\TransportException","message":"Failed to authenticate on SMTP server with username \"5e46f43dd81cb4\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\".","code":535}  
[2025-09-03 03:09:05] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-03 05:09:46] local.INFO: user login from 127.0.0.1  
[2025-09-03 05:16:12] local.ERROR: {"query":[],"parameter":{"domainName":"lamtpeor.net","userEmail":"<EMAIL>","domainId":152,"createdDate":"2025-09-03 05:04:18","userID":7,"email":"<EMAIL>"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column domain_cancellation_requests.created_at does not exist
LINE 1: ...rEmail\", \"domain_cancellation_requests\".\"reason\", \"domain_ca...
                                                             ^ (Connection: client, SQL: select \"domain_cancellation_requests\".\"domain_id\" as \"domainId\", \"domains\".\"name\" as \"domainName\", \"domain_cancellation_requests\".\"user_id\" as \"userID\", \"users\".\"email\" as \"userEmail\", \"domain_cancellation_requests\".\"reason\", \"domain_cancellation_requests\".\"created_at\" from \"domain_cancellation_requests\" inner join \"domains\" on \"domain_cancellation_requests\".\"domain_id\" = \"domains\".\"id\" inner join \"users\" on \"domain_cancellation_requests\".\"user_id\" = \"users\".\"id\" where \"domain_cancellation_requests\".\"domain_id\" = 152 limit 1)","code":"42703"}  
[2025-09-03 05:17:44] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-03 05:17:44] local.INFO: The domain is refunded:   
[2025-09-03 05:18:01] local.ERROR: {"query":[],"parameter":{"domainName":"lamtpeor.net","userEmail":"<EMAIL>","domainId":152,"createdDate":"2025-09-03 05:04:18","userID":7,"email":"<EMAIL>"},"error":"ErrorException","message":"Trying to access array offset on value of type null","code":0}  
