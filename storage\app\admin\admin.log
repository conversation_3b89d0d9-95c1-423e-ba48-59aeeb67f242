[2025-09-03 05:28:22] local.INFO: Domain History: Domain deletion request approved by admin 1 (a@a.a)  
[2025-09-03 05:28:22] local.INFO: Domain creation date: 2025-09-03 05:05:55, Current date: 2025-09-03 05:28:22, Is refunded: false  
[2025-09-03 05:28:22] local.INFO: The domain is refunded: false  
[2025-09-03 05:28:26] local.ERROR: {"query":[],"parameter":{"domainName":"aslwert.org","userEmail":"<EMAIL>","domainId":155,"createdDate":"2025-09-03 05:04:18","userID":7,"email":"<EMAIL>"},"error":"Symfony\\Component\\Mailer\\Exception\\TransportException","message":"Failed to authenticate on SMTP server with username \"5e46f43dd81cb4\" using the following authenticators: \"CRAM-MD5\", \"LOGIN\", \"PLAIN\". Authenticator \"CRAM-MD5\" returned \"Expected response code \"235\" but got code \"535\", with message \"535 5.7.0 The email limit is reached. Please upgrade your plan https:\/\/mailtrap.io\/billing\/plans\/testing\".\". Authenticator \"LOGIN\" returned \"Expected response code \"334\" but got empty code.\". Authenticator \"PLAIN\" returned \"Expected response code \"235\" but got empty code.\".","code":535}  
[2025-09-03 05:30:20] local.ERROR: {"query":[],"parameter":{"domainId":147,"createdDate":"2025-09-03 04:55:26","support_note":"Domain deletion request rejected."},"error":"ErrorException","message":"Attempt to read property \"domainId\" on array","code":0}  
