<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['url']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['url']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<tr>
    <td class="header">
        <a href="<?php echo new \Illuminate\Support\EncodedHtmlString($url); ?>" style="display: inline-block;">
            <div style="padding: 0.75rem; background-color: #3490dc; border-radius: 0.25rem; color: white; font-weight: bold;">
                SD
            </div>
        </a>
        <div style="margin-top: 0.5rem; font-size: 0.8rem; font-weight: bold;">
            STRANGEDOMAINS
        </div>
    </td>
</tr>
<?php /**PATH C:\1xampp\htdocs\sd-admin\resources\views/vendor/mail/html/header.blade.php ENDPATH**/ ?>