<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Client\Constants\DomainStatus;
use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DatabaseQueryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $DatabaseQueryService = new self;

        return $DatabaseQueryService;
    }

    public function get(ShowListRequest $request)
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = self::baseQuery();
        self::whenHasDomain($builder, $request);
        self::whenHasEmail($builder, $request);
        self::whenHasStatusType($builder, $request);
        self::whenHasOrderby($builder, $request);
        $builder = $builder->paginate($pageLimit)->withQueryString();
        return [
            ...CursorPaginate::cursor($builder, self::paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }
    public function supportNoteSave(ShowListRequest $request)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('id', $request->deletion_id)
            ->update([
                'support_note' => $request->support_note,
                'support_agent_name' => Auth::user()->name . ' (' . Auth::user()->email . ')',
                'feedback_date' => now(),
            ]);
        //return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    public function getDomainInfo($domainId)
    {
        // First check if domain exists
        $domainExists = DB::client()->table('domains')->where('id', $domainId)->exists();
        if (!$domainExists) {
            \Log::error("Domain with ID {$domainId} does not exist in domains table");
            return null;
        }

        $requestExists = DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $domainId)->exists();
        if (!$requestExists) {
            \Log::error("Domain cancellation request for domain ID {$domainId} does not exist");
            return null;
        }

        return DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'users.first_name',
                'users.last_name',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at'
            ])
            ->where('domain_cancellation_requests.domain_id', $domainId)
            ->first();
    }

    public function getExpiredApprovedRequests()
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domains.status as domainStatus',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNotNull('domain_cancellation_requests.support_agent_id')
            ->whereNotNull('domain_cancellation_requests.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->where('domain_cancellation_requests.feedback_date', '<=', now()->subHours(24))
            ->limit(500)
            ->get();
    }
    
    // PRIVATE Functions

    private function baseQuery(): Builder
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->join('registered_domains', 'domain_cancellation_requests.domain_id', '=', 'registered_domains.domain_id')
            ->select(self::getSelectFields());
    }

    private function getSelectFields(): array
    {
        return [
            'domains.name as domainName',
            'domains.status',
            'domains.deleted_at as domainDeletedAt',
            'domains.created_at',
            'users.id as user_id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'domain_cancellation_requests.deleted_at',
            'domain_cancellation_requests.requested_at',
            'domain_cancellation_requests.reason',
            'domain_cancellation_requests.id as dcrID',
            'domain_cancellation_requests.support_agent_id',
            'domain_cancellation_requests.support_agent_name',
            'domain_cancellation_requests.support_note',
            'domain_cancellation_requests.feedback_date',
            'domain_cancellation_requests.domain_id',
            'registered_domains.status as rstatus'

        ];
    }

    private function whenHasStatusType(Builder &$builder, ShowListRequest $request): void
    {
         if (!$request->has('statusType')) {
            return;
        }

        match ($request->statusType) {
            StatusTypes::ALL => null,
            StatusTypes::PENDING => $builder->whereNull('domain_cancellation_requests.deleted_at')
                                            ->whereNull('domain_cancellation_requests.feedback_date'),
            StatusTypes::APPROVED => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                                            ->whereNotNull('domain_cancellation_requests.feedback_date')
                                            ->where('domains.status', DomainStatus::IN_PROCESS),
            StatusTypes::REJECTED => $builder->whereNull('domain_cancellation_requests.deleted_at')
                                            ->whereNotNull('domain_cancellation_requests.feedback_date')
                                            ->where('domains.status', DomainStatus::ACTIVE),
            StatusTypes::CANCELLED => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                                             ->whereNull('domain_cancellation_requests.feedback_date')
                                             ->where('domains.status', DomainStatus::ACTIVE),
            StatusTypes::DELETED => $builder->where('domains.status', DomainStatus::DELETED),
            default => $builder->whereNull('domain_cancellation_requests.deleted_at')
        };
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);

            if (count($orderby) == 2 && in_array($orderby[1], ['asc', 'desc'])) {
                switch ($orderby[0]) {
                    case 'domain':
                        $query->orderBy('domainName', $orderby[1]);
                        break;
                    case 'requested_at':
                        $query->orderBy('domain_cancellation_requests.requested_at', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('dcrID', 'desc');
                }
            } else {
                $query->orderBy('dcrID', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('dcrID', 'desc');
            });
    }

    private static function whenHasDomain(&$builder, $request)
    {
        $builder->when(($request->has('domain') || $request->has('search')), function (Builder $query) use ($request) {
            $domain = $request->domain ?? $request->search;
            $query->where('name', 'ilike', $domain . '%');
        });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email . '%');
        });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('statusType')) {
            $param[] = 'statusType=' . $request->statusType;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
